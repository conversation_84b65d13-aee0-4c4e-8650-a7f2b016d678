{"name": "<PERSON><PERSON><PERSON>", "type": "module", "scripts": {"build": "curl -sSL https://get.wasp.sh/installer.sh | sh -s -- -v 0.15.0 && export PATH=\"$HOME/.local/bin:$PATH\" && npm install && wasp build", "start": "wasp start", "dev": "wasp start", "test": "echo 'Tests will be available after wasp start'", "test:e2e": "echo 'E2E tests will be available after wasp start'", "analyze": "node scripts/analyze-bundle.js", "vercel-build": "bash scripts/build-for-vercel.sh", "build:production": "NODE_ENV=production npm run vercel-build", "build:local": "bash scripts/build-local-for-vercel.sh", "deploy": "vercel --prod", "deploy:preview": "vercel", "deploy:local": "bash scripts/deploy-to-vercel.sh", "db:generate": "wasp db generate", "db:migrate": "wasp db migrate-deploy", "db:studio": "wasp db studio"}, "dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "2.8.0", "@emotion/react": "11.10.6", "@emotion/styled": "11.10.6", "@prisma/client": "5.19.1", "@prisma/engines": "5.19.1", "@types/cors": "^2.8.5", "@types/uuid": "^10.0.0", "bolt11": "1.4.1", "cors": "^2.8.5", "express": "~4.21.0", "express-rate-limit": "^7.1.5", "framer-motion": "^6.5.1", "helmet": "^6.0.0", "jsonwebtoken": "^8.5.1", "lnurl": "0.24.2", "node-fetch": "3.3.0", "openai": "^4.103.0", "pdfjs-dist": "3.3.122", "pg": "^8.16.0", "pg-types": "^4.0.2", "qrcode.react": "3.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.56.4", "react-icons": "4.8.0", "react-router-dom": "^6.26.2", "stripe": "13.3.0", "uuid": "^11.1.0", "wasp": "file:.wasp/out/sdk/wasp", "zod": "^3.22.4", "vite": "^5.0.0"}, "devDependencies": {"@types/body-parser": "^1.19.5", "@types/express": "^4.17.13", "@types/express-serve-static-core": "^4.17.13", "@types/node": "^18.0.0", "@types/jsonwebtoken": "^8.5.9", "@types/jest": "^29.5.0", "@types/react": "^18.0.37", "@types/react-dom": "^18.0.11", "prisma": "5.19.1", "typescript": "^5.1.0", "vercel": "^42.1.1", "rollup": "^4.9.6", "@vercel/node": "^3.0.0", "@vercel/static-build": "^2.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}